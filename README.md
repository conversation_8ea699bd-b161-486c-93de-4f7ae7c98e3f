# Estudo Organizado - Backend

Sistema de planejamento e controle de estudos para concursos públicos, desenvolvido com Spring Boot.

## 📋 Sobre o Projeto

O **Estudo Organizado** é uma aplicação backend que oferece ferramentas completas para organização e controle de estudos, especialmente voltada para concursos públicos. O sistema permite aos usuários criar planejamentos personalizados, gerenciar ciclos de estudo, acompanhar o progresso e utilizar técnicas de revisão espaçada para otimizar o aprendizado.

## 🚀 Funcionalidades

### 🔐 Autenticação e Perfil
- **Registro e Login**: Sistema de autenticação com JWT
- **Gerenciamento de Perfil**: Atualização de dados pessoais e foto do usuário

### 📚 Gestão de Disciplinas
- **CRUD de Disciplinas**: <PERSON><PERSON><PERSON>, listar, editar e excluir disciplinas de estudo
- **Organização**: Disciplinas organizadas por usuário

### 📅 Planejamento de Estudos
- **Criação de Planejamentos**: Definir horas semanais disponíveis e duração máxima por sessão
- **Sugestão Automática**: Algoritmo inteligente para distribuição de tempo entre disciplinas
- **Configuração de Intervalos**: Personalização dos intervalos de revisão espaçada

### 🔄 Ciclos de Estudo
- **Criação de Ciclos**: Organizar disciplinas em ciclos de estudo otimizados
- **Distribuição Inteligente**: Algoritmo que distribui tempo proporcionalmente baseado em peso e nível de conhecimento
- **Alternância de Disciplinas**: Sistema que garante alternância entre disciplinas para evitar fadiga mental
- **Aproveitamento Total**: Utilização completa das horas semanais disponíveis
- **Controle de Progresso**: Acompanhamento do andamento de cada ciclo

### 📝 Registro de Estudos
- **Sessões de Estudo**: Registrar tempo estudado, descrição e resultados de questões
- **Vinculação**: Associar registros a disciplinas específicas do ciclo
- **Histórico**: Manter histórico completo de estudos realizados

### 🧠 Revisão Espaçada
- **Algoritmo SM-2 Modificado**: Sistema baseado em pesquisas científicas de memorização
- **Intervalos Configuráveis**: Padrão de 1, 3, 7, 14, 30, 60, 120 dias (personalizável)
- **Níveis de Dificuldade**: Ajuste automático baseado na dificuldade percebida
- **Revisões Adicionais**: Geração automática de revisões extras para conteúdos difíceis

### 🤖 Inteligência Artificial
- **Assistente Virtual**: Chat inteligente para auxílio nos estudos
- **Planejamento com IA**: Geração automática de planejamentos personalizados
- **Integração Vertex AI**: Utilização do Google Gemini para respostas contextuais

### ⚙️ Configurações
- **Personalização**: Ajuste de intervalos de revisão espaçada
- **Preferências**: Configurações individuais por usuário

## 🛠️ Tecnologias Utilizadas

- **Java 17**
- **Spring Boot 3.1.3**
- **Spring Security** (Autenticação JWT)
- **Spring Data JPA** (Persistência de dados)
- **PostgreSQL** (Banco de dados)
- **Spring AI** (Integração com Vertex AI)
- **SpringDoc OpenAPI** (Documentação da API)
- **ModelMapper** (Mapeamento de objetos)
- **Lombok** (Redução de código boilerplate)
- **Maven** (Gerenciamento de dependências)

## 📦 Pré-requisitos

- **Java 17** ou superior
- **Maven 3.6** ou superior
- **PostgreSQL 12** ou superior
- **Docker** (opcional, para execução com containers)

## 🔧 Instalação e Configuração

### 1. Clone o repositório
```bash
git clone <url-do-repositorio>
cd estudo-organizado-ms
```

### 2. Configure o banco de dados

#### Opção A: Usando Docker (Recomendado)
```bash
docker-compose up -d
```
Isso irá criar um container PostgreSQL com as configurações necessárias.

#### Opção B: PostgreSQL local
1. Instale o PostgreSQL
2. Crie um banco de dados chamado `estudoorganizado`
3. Ajuste as configurações em `application.properties` se necessário

### 3. Configure as variáveis de ambiente (opcional)

Para usar a integração com IA, configure:
```bash
export GOOGLE_APPLICATION_CREDENTIALS=caminho/para/credenciais.json
```

### 4. Execute a aplicação

```bash
# Compilar o projeto
mvn clean compile

# Executar os testes
mvn test

# Executar a aplicação
mvn spring-boot:run
```

A aplicação estará disponível em: `http://localhost:8080`

## 📖 Documentação da API

Após iniciar a aplicação, acesse a documentação interativa da API:

- **Swagger UI**: `http://localhost:8080/swagger-ui.html`
- **OpenAPI JSON**: `http://localhost:8080/v3/api-docs`

## 🔍 Endpoints Principais

### Autenticação
- `POST /v1/auth/register` - Registrar novo usuário
- `POST /v1/auth/authenticate` - Login do usuário

### Disciplinas
- `GET /v1/disciplinas` - Listar disciplinas
- `POST /v1/disciplinas` - Criar disciplina
- `PUT /v1/disciplinas/{id}` - Atualizar disciplina
- `DELETE /v1/disciplinas/{id}` - Excluir disciplina

### Planejamento
- `GET /v1/planejamento` - Buscar planejamento do usuário
- `POST /v1/planejamento` - Criar/atualizar planejamento
- `POST /v1/planejamento/sugerir` - Sugerir ciclo de estudo

### Ciclos de Estudo
- `GET /v1/ciclo-estudo` - Listar ciclos
- `POST /v1/ciclo-estudo` - Criar ciclo
- `GET /v1/ciclo-estudo/atual` - Buscar ciclo atual
- `POST /v1/ciclo-estudo/registrar-estudo` - Registrar sessão de estudo

### Revisão Espaçada
- `GET /v1/revisoes-espacadas/pendentes` - Revisões pendentes para hoje
- `GET /v1/revisoes-espacadas/todas-pendentes` - Todas as revisões pendentes
- `PUT /v1/revisoes-espacadas/concluir/{id}` - Marcar revisão como concluída

### Assistente IA
- `POST /v1/assistente-ia/chat` - Chat com assistente
- `GET /v1/assistente-ia/conversas` - Listar conversas

### Planejamento IA
- `POST /v1/planejamento-ia/gerar` - Gerar planejamento com IA

## 🏗️ Arquitetura

O projeto segue uma arquitetura em camadas:

```
├── controller/     # Controladores REST
├── service/        # Lógica de negócio
├── repository/     # Acesso a dados
├── model/          # Entidades JPA
├── dto/            # Objetos de transferência
├── config/         # Configurações
├── security/       # Segurança e JWT
├── exception/      # Tratamento de exceções
└── util/           # Utilitários
```

## 🧪 Testes

```bash
# Executar todos os testes
mvn test

# Executar testes com relatório de cobertura
mvn test jacoco:report
```

## 🚀 Deploy

### Build para produção
```bash
mvn clean package -DskipTests
```

O arquivo JAR será gerado em `target/estudo-organizado-ms-0.0.1-SNAPSHOT.jar`

### Executar em produção
```bash
java -jar target/estudo-organizado-ms-0.0.1-SNAPSHOT.jar
```

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 📞 Suporte

Para dúvidas ou suporte, entre em contato através dos issues do GitHub.