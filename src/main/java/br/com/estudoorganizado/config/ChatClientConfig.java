package br.com.estudoorganizado.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class ChatClientConfig {

    @Bean
    public ChatClient chatClient(ChatClient.Builder chatClientBuilder) {
        // Log para debug
        System.out.println("Criando ChatClient para funções...");

        return chatClientBuilder
                .defaultFunctions("cadastrarDisciplina", "criarPlanejamento", "salvarOuAtualizarPlanejamento")
                .defaultSystem("Você é um assistente especializado em planejamento de estudos para concursos públicos. " +
                        "Você pode ajudar os usuários a cadastrar disciplinas, criar e gerenciar planejamentos de estudo. " +
                        "Seja sempre educado, claro e objetivo em suas respostas. " +
                        "Solicite as informações necessárias de forma progressiva, um por vez ou agrupe as perguntas relacionadas. " +
                        "Quando o usuário solicitar uma ação específica, use as funções disponíveis para executá-la." +
                        "Antes de executar a ação ou chamar a função, peça para o usuário conferir e confirmar." +
                        "Se o usuário confirmar, execute a ação ou chame a função." +
                        "Não diga que está criando ou executando a função, apenas execute a ação após a confirmação.")
                .build();
    }

    @Bean
    @Qualifier("contentGenerationChatClient")
    public ChatClient contentGenerationChatClient(ChatClient.Builder chatClientBuilder) {
        // Utilizar o modelo gemini-2.5-pro para geração de conteúdo
        System.out.println("Criando ChatClient para geração de conteúdo...");
        return chatClientBuilder
                .defaultSystem("Você é um especialista em planejamento de estudos para concursos públicos, com vasta experiência na criação de ciclos de estudo eficientes e personalizados. Sua especialidade é traduzir as necessidades e dificuldades dos alunos em um plano estruturado.")
                .build();
    }
}
