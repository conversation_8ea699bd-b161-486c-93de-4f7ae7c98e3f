package br.com.estudoorganizado.model;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "disciplina_topico")
public class DisciplinaTopico {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String descricao;

    @ManyToOne
    @JoinColumn(name = "disciplina_id", foreignKey = @ForeignKey(name = "fk_disciplinatopico_disciplina"))
    @com.fasterxml.jackson.annotation.JsonBackReference
    @lombok.ToString.Exclude
    private Disciplina disciplina;

    @ManyToOne
    @JoinColumn(name = "user_id", foreignKey = @ForeignKey(name = "fk_disciplinatopico_user"))
    private User user;
}
