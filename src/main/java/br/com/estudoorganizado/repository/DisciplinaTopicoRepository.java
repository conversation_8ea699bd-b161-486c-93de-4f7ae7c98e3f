package br.com.estudoorganizado.repository;

import br.com.estudoorganizado.model.DisciplinaTopico;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface DisciplinaTopicoRepository extends JpaRepository<DisciplinaTopico, Long> {

    Optional<DisciplinaTopico> findByIdAndUserId(Long id, Long userId);

    List<DisciplinaTopico> findByDisciplinaIdAndUserId (Long disciplinaId, Long userId);

}
