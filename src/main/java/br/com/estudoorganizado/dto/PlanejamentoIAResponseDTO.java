package br.com.estudoorganizado.dto;

import lombok.Data;
import java.util.List;

@Data
public class PlanejamentoIAResponseDTO {
    
    private String nomePlanejamento;
    private String descricaoGeral;
    private Integer horasDisponiveisPorSemana;
    private Integer minutosDuracaoMaximaPorSessao;
    private String intervalosRevisao;
    private CicloEstudoIADTO cicloEstudo;

    @Data
    public static class CicloEstudoIADTO {
        private String nome;
        private List<DisciplinaIADTO> disciplinas;
    }
}
