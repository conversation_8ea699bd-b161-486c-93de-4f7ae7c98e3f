package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.CicloEstudoAtualDTO;
import br.com.estudoorganizado.dto.CicloEstudoDTO;
import br.com.estudoorganizado.dto.IndicadoresCicloDTO;
import br.com.estudoorganizado.dto.RegistroEstudoDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.CicloEstudoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/ciclo-estudo")
@RequiredArgsConstructor
@Tag(name = "Ciclo de Estudo", description = "Endpoints para gerenciar ciclos de estudo")
public class CicloEstudoController {

    private final CicloEstudoService cicloEstudoService;

    @PostMapping
    @Operation(summary = "Criar ciclo de estudo", description = "Cria um novo ciclo de estudo com disciplinas")
    public ResponseEntity<CicloEstudoDTO> save(@RequestBody @Valid CicloEstudoDTO cicloEstudoDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.status(HttpStatus.CREATED).body(cicloEstudoService.save(cicloEstudoDTO, user));
    }

    @GetMapping
    @Operation(summary = "Listar ciclos de estudo", description = "Retorna todos os ciclos de estudo do usuário")
    public ResponseEntity<List<CicloEstudoDTO>> findAll(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findAllByUserId(user.getId()));
    }

    @GetMapping("/concluidos")
    @Operation(summary = "Listar ciclos concluídos", description = "Retorna todos os ciclos de estudo concluídos do usuário")
    public ResponseEntity<List<CicloEstudoDTO>> findAllConcluidos(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findAllConcluidos(user.getId()));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Buscar ciclo por ID", description = "Retorna um ciclo de estudo específico pelo ID")
    public ResponseEntity<CicloEstudoDTO> findById(@PathVariable Long id, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findByIdAndUserId(id, user.getId()));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Atualizar ciclo de estudo", description = "Atualiza um ciclo de estudo existente")
    public ResponseEntity<CicloEstudoDTO> update(@PathVariable Long id, @RequestBody @Valid CicloEstudoDTO cicloEstudoDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.update(id, user, cicloEstudoDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Excluir ciclo de estudo", description = "Remove um ciclo de estudo do usuário")
    public ResponseEntity<Void> delete(@PathVariable Long id, @AuthenticationPrincipal User user) {
        cicloEstudoService.delete(id, user.getId());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/atual")
    @Operation(summary = "Buscar ciclo atual", description = "Retorna o ciclo de estudo atual do usuário")
    public ResponseEntity<CicloEstudoAtualDTO> findCicloEstudoAtual(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.findCicloEstudoAtual(user.getId()));
    }

    @PostMapping("/registrar-estudo")
    @Operation(summary = "Registrar estudo", description = "Registra uma sessão de estudo em uma disciplina do ciclo")
    public ResponseEntity<String> registrarEstudo(@RequestBody @Valid RegistroEstudoDTO registroEstudoDTO, @AuthenticationPrincipal User user) {
        cicloEstudoService.registrarEstudo(registroEstudoDTO, user);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/desvincular-registro-estudo/{cicloEstudoDisciplinaId}")
    @Operation(summary = "Desvincular registros", description = "Remove a vinculação de registros de estudo de uma disciplina do ciclo")
    public ResponseEntity<String> desvincularRegistrosEstudo(@PathVariable Long cicloEstudoDisciplinaId, @AuthenticationPrincipal User user) {
        cicloEstudoService.desvincularRegistrosEstudo(cicloEstudoDisciplinaId, user);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/reiniciar-ciclo-estudo/{cicloEstudoId}")
    @Operation(summary = "Reiniciar ciclo", description = "Reinicia um ciclo de estudo removendo todos os registros")
    public ResponseEntity<String> reiniciarCicloEstudo(@PathVariable Long cicloEstudoId, @AuthenticationPrincipal User user) {
        cicloEstudoService.reiniciarCicloEstudo(cicloEstudoId, user);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/indicadores-ciclo")
    public ResponseEntity<IndicadoresCicloDTO> indicadoresCiclos(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(cicloEstudoService.indicadoresCiclos(user.getId()));
    }
}
