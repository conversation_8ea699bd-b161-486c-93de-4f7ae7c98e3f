package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.dto.DisciplinaTopicoDTO;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.service.DisciplinaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/v1/disciplinas")
@RequiredArgsConstructor
@Tag(name = "Disciplinas", description = "Endpoints para gerenciar disciplinas")
public class DisciplinaController {

    private final DisciplinaService disciplinaService;

    @PostMapping
    @Operation(summary = "Criar disciplina", description = "Cria uma nova disciplina para o usuário")
    public ResponseEntity<DisciplinaDTO> save(@RequestBody @Valid DisciplinaDTO disciplinaDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.status(HttpStatus.CREATED).body(disciplinaService.save(disciplinaDTO, user));
    }

    @GetMapping
    @Operation(summary = "Listar disciplinas", description = "Retorna todas as disciplinas do usuário")
    public ResponseEntity<List<DisciplinaDTO>> findAll(@AuthenticationPrincipal User user) {
        return ResponseEntity.ok(disciplinaService.findAllByUserId(user.getId()));
    }

    @GetMapping("/{id}")
    @Operation(summary = "Buscar disciplina por ID", description = "Retorna uma disciplina específica pelo ID")
    public ResponseEntity<DisciplinaDTO> findById(@PathVariable Long id, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(disciplinaService.findByIdAndUserId(id, user.getId()));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Atualizar disciplina", description = "Atualiza uma disciplina existente")
    public ResponseEntity<DisciplinaDTO> update(@PathVariable Long id, @RequestBody @Valid DisciplinaDTO disciplinaDTO, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(disciplinaService.update(id, user.getId(), disciplinaDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Excluir disciplina", description = "Remove uma disciplina do usuário")
    public ResponseEntity<Void> delete(@PathVariable Long id, @AuthenticationPrincipal User user) {
        disciplinaService.delete(id, user.getId());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{disciplinaId}/topicos")
    @Operation(summary = "Buscar topicos por disciplina ID", description = "Retorna os tópicos de uma disciplina específica pelo ID")
    public ResponseEntity<List<DisciplinaTopicoDTO>> consultarTopicos(@PathVariable Long disciplinaId, @AuthenticationPrincipal User user) {
        return ResponseEntity.ok(disciplinaService.consultarTopicosPorDisciplinaIdAndUserId(disciplinaId, user.getId()));
    }
}
