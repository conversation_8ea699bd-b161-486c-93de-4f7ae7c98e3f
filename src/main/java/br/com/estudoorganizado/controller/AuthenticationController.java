package br.com.estudoorganizado.controller;

import br.com.estudoorganizado.dto.AuthResponseDTO;
import br.com.estudoorganizado.dto.AuthenticationDTO;
import br.com.estudoorganizado.dto.UserRegistrationDTO;
import br.com.estudoorganizado.service.AuthenticationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/auth")
@RequiredArgsConstructor
@Tag(name = "Authentication", description = "Endpoints para autenticação e registro de usuários")
public class AuthenticationController {

    private final AuthenticationService authenticationService;

    @PostMapping("/register")
    @Operation(summary = "Registrar novo usuário", description = "Cria uma nova conta de usuário no sistema")
    public ResponseEntity<String> register(@RequestBody UserRegistrationDTO request) {
        return ResponseEntity.ok(authenticationService.register(request));
    }

    @PostMapping("/authenticate")
    @Operation(summary = "Autenticar usuário", description = "Realiza login do usuário e retorna token JWT")
    public ResponseEntity<AuthResponseDTO> authenticate(@RequestBody AuthenticationDTO request) {
        return ResponseEntity.ok(authenticationService.authenticate(request));
    }
} 