package br.com.estudoorganizado.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/v1/vertex-ai")
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
@Tag(name = "Vertex AI Test", description = "Endpoints para testar integração com Vertex AI")
public class VertexAITestController {

    private final ChatClient contentGenerationChatClient;

    public VertexAITestController(@Qualifier("contentGenerationChatClient") ChatClient contentGenerationChatClient) {
        this.contentGenerationChatClient = contentGenerationChatClient;
    }

    @GetMapping("/health")
    @Operation(summary = "Verificar saúde do Vertex AI", description = "Verifica se a integração com Vertex AI está funcionando")
    public ResponseEntity<Map<String, String>> health() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Vertex AI Gemini");
        response.put("timestamp", java.time.Instant.now().toString());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/test-simple")
    @Operation(summary = "Teste simples do Vertex AI", description = "Testa comunicação básica com o modelo Vertex AI Gemini")
    public ResponseEntity<Map<String, Object>> testSimple(@RequestParam(defaultValue = "Olá, como você está?") String message) {
        try {
            log.info("Testando comunicação simples com Vertex AI");
            
            var chatResponse = contentGenerationChatClient.prompt()
                .user(message)
                .call()
                .chatResponse();
            
            Map<String, Object> response = new HashMap<>();
            
            if (chatResponse == null) {
                response.put("success", false);
                response.put("error", "ChatResponse é nulo");
                return ResponseEntity.ok(response);
            }
            
            if (chatResponse.getResult() == null) {
                response.put("success", false);
                response.put("error", "ChatResponse.getResult() é nulo");
                response.put("metadata", chatResponse.getMetadata());
                return ResponseEntity.ok(response);
            }
            
            String content = chatResponse.getResult().getOutput().getContent();
            
            response.put("success", true);
            response.put("message", message);
            response.put("response", content);
            response.put("metadata", chatResponse.getMetadata());
            
            log.info("Teste simples realizado com sucesso");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Erro no teste simples: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
            
            return ResponseEntity.ok(response);
        }
    }

    @GetMapping("/test-json")
    public ResponseEntity<Map<String, Object>> testJson() {
        try {
            log.info("Testando geração de JSON com Vertex AI");
            
            String prompt = "Gere um JSON simples com as seguintes informações: nome: 'Teste', idade: 25, ativo: true. " +
                           "Retorne APENAS o JSON válido, sem explicações ou formatação adicional.";
            
            var chatResponse = contentGenerationChatClient.prompt()
                .user(prompt)
                .call()
                .chatResponse();
            
            Map<String, Object> response = new HashMap<>();
            
            if (chatResponse == null || chatResponse.getResult() == null) {
                response.put("success", false);
                response.put("error", "Resposta nula da IA");
                return ResponseEntity.ok(response);
            }
            
            String content = chatResponse.getResult().getOutput().getContent();
            
            response.put("success", true);
            response.put("prompt", prompt);
            response.put("response", content);
            response.put("responseLength", content != null ? content.length() : 0);
            
            log.info("Teste JSON realizado com sucesso");
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Erro no teste JSON: {}", e.getMessage(), e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
            
            return ResponseEntity.ok(response);
        }
    }
}
