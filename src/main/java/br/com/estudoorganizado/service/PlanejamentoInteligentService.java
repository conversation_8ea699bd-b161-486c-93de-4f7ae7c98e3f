package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.*;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.util.Util;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@ConditionalOnProperty(name = "utilizar-spring-ai", havingValue = "true")
public class PlanejamentoInteligentService {

    private final ChatClient contentGenerationChatClient;
    private final PlanejamentoService planejamentoService;
    private final DisciplinaService disciplinaService;
    private final Gson gson = new Gson();

    public PlanejamentoInteligentService(
            @Qualifier("contentGenerationChatClient") ChatClient contentGenerationChatClient,
            PlanejamentoService planejamentoService,
            DisciplinaService disciplinaService) {
        this.contentGenerationChatClient = contentGenerationChatClient;
        this.planejamentoService = planejamentoService;
        this.disciplinaService = disciplinaService;
    }

    public PlanejamentoDTO criarPlanejamentoComIA(PlanejamentoIARequestDTO request, User user) {
        try {
            log.info("Iniciando criação de planejamento com IA para usuário: {}", user.getEmail());

            String prompt = gerarPrompt(request);
            log.debug("Prompt gerado: {}", prompt);

            // Obter resposta da IA usando ChatClient específico para geração de conteúdo
            log.info("Enviando prompt para IA...");

            String respostaIA;
            try {
                var chatResponse = contentGenerationChatClient.prompt()
                    .user(prompt)
                    .call()
                    .chatResponse();

                if (chatResponse == null || chatResponse.getResult() == null) {
                    log.error("Resposta da IA é nula ou vazia");
                    throw new RuntimeException("A IA não retornou uma resposta válida");
                }

                respostaIA = chatResponse.getResult().getOutput().getContent();
                log.info("Resposta recebida da IA com {} caracteres", respostaIA != null ? respostaIA.length() : 0);

            } catch (Exception e) {
                log.error("Erro ao chamar a IA: {}", e.getMessage(), e);
                throw new RuntimeException("Erro na comunicação com a IA: " + e.getMessage(), e);
            }

            if (respostaIA == null || respostaIA.trim().isEmpty()) {
                log.error("Resposta da IA está vazia");
                throw new RuntimeException("A IA retornou uma resposta vazia");
            }

            PlanejamentoIAResponseDTO respostaProcessada = processarRespostaIA(respostaIA);
            PlanejamentoDTO planejamentoDTO = converterParaPlanejamentoDTO(respostaProcessada, user);
            PlanejamentoDTO planejamentoSalvo = planejamentoService.saveOrUpdate(planejamentoDTO, user);

            log.info("Planejamento criado com sucesso via IA para usuário: {}", user.getEmail());
            return planejamentoSalvo;

        } catch (Exception e) {
            e.printStackTrace();
            log.error("Erro ao criar planejamento com IA: {}", e.getMessage());
            throw new RuntimeException("Erro ao processar resposta da IA: " + e.getMessage(), e);
        }
    }

    private String gerarPrompt(PlanejamentoIARequestDTO planejamentoIADTO) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("# TAREFA PRINCIPAL\n");
        prompt.append("Sua tarefa é criar um planejamento de estudos detalhado em formato de ciclo semanal, baseado nas informações fornecidas. O resultado final deve ser ESTRITAMENTE um objeto JSON válido, sem nenhum texto, explicação ou formatação adicional.\n");

        prompt.append("# PROCESSO DE RACIOCÍNIO OBRIGATÓRIO\n");
        prompt.append("Para construir o planejamento, siga estes passos lógicos:\n");
        prompt.append("1.  **Calcular a Prioridade:** Para cada disciplina, calcule um \"score de prioridade\". Uma boa fórmula é: `Prioridade = (Peso * 5) / NivelConhecimento`. Disciplinas com maior peso e menor conhecimento terão maior prioridade.\n");
        prompt.append("2.  **Distribuir o Tempo Total:** Distribua as `horasDisponiveisPorSemana` entre as disciplinas proporcionalmente ao score de prioridade de cada uma. O resultado será o tempo total que o aluno dedicará a cada disciplina durante a semana.\n");
        prompt.append("3.  **Dividir em Sessões (A ETAPA MAIS CRÍTICA):** Para cada disciplina, pegue o tempo total calculado no passo anterior e divida-o em sessões de estudo.\n");
        prompt.append("    * Cada sessão individual (`tempoEstudoMeta`) **NÃO PODE** exceder o valor de `minutosDuracaoMaximaPorSessao`.\n");
        prompt.append("    * Se `minutosDuracaoMaximaPorSessao` for `null`, adote um padrão de **90 minutos**.\n");
        prompt.append("    * **Exemplo Concreto:** Se \"Direito Administrativo\" tem um tempo total calculado de 4 horas (240 minutos) e a sessão máxima é de 90 minutos, você deve criar múltiplas entradas para essa disciplina no ciclo. Por exemplo: uma sessão de 90 min, outra de 90 min e uma terceira de 60 min. Cada uma será um objeto diferente na lista `disciplinas` do JSON.\n");
        prompt.append("    * **Organize todas as sessões criadas em uma sequência lógica (`ordem`), evite colocar disciplinas iguais uma após a outra, tente preferencialmente ir alternando disciplinas de alta , media e baixa prioridade para otimizar a atenção do aluno. Exemplo: 1 - Português(Prioridade Alta); 2 - Matemática(Prioridade Média); 3 - Português(Prioridade Alta); 4 - Direito Administrativo(Prioridade Média)\n");
        prompt.append("5.  **Verificação Final:** A soma de todos os campos `tempoEstudoMeta` no JSON final deve ser exatamente igual ao tempo total disponível na semana.\n");

        prompt.append("# INFORMAÇÕES DO USUÁRIO:\n");
        prompt.append("- Objetivo: ").append(planejamentoIADTO.getNomePlanejamento()).append("\n");
        prompt.append("- Horas disponíveis por semana: ").append(planejamentoIADTO.getHorasDisponiveisPorSemana()).append("\n");
        prompt.append("- Duração máxima por sessão:").append(Util.emptyNumber(planejamentoIADTO.getMinutosDuracaoMaximaPorSessao()) ? 90 : planejamentoIADTO.getMinutosDuracaoMaximaPorSessao()).append(" minutos \n");
        prompt.append("- Duração minima por sessão: 30 minutos\n");
        prompt.append("- Disciplinas: \n");
        planejamentoIADTO.getDisciplinas().forEach(
                d -> prompt.append(String.format("\t- Nome Disciplina: %s - Nivel Conhecimento: %s - Peso: %s",
                        d.getNome(),
                        Util.emptyNumber(d.getNivelConhecimentoAtual()) ? 0 : d.getNivelConhecimentoAtual(),
                        Util.emptyNumber(d.getPeso()) ? 1 : d.getPeso())).append(".\n")
        );

        prompt.append("# ESTRUTURA DE SAÍDA (RETORNE APENAS ESTE JSON):\n");
        prompt.append(obterJsonModeloPlanejamentoIA());

        return prompt.toString();
    }

    private String obterJsonModeloPlanejamentoIA() {
        return "{\n" +
                "  \"nomePlanejamento\": \"Nome do planejamento\",\n" +
                "  \"horasDisponiveisPorSemana\": 22,\n" +
                "  \"minutosDuracaoMaximaPorSessao\": null,\n" +
                "  \"intervalosRevisao\": \"1,3,7,14,30,60,120\",\n" +
                "  \"cicloEstudo\": {\n" +
                "    \"nome\": \"Ciclo de Estudos - Concurso Federal\",\n" +
                "    \"disciplinas\": [\n" +
                "      {\n" +
                "        \"nome\": \"Nome da disciplina\",\n" +
                "        \"peso\": 5,\n" +
                "        \"nivelConhecimento\": 3,\n" +
                "        \"ordem\": 1,\n" +
                "        \"tempoEstudoMeta\": \"01:30:00\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";
    }

    private PlanejamentoIAResponseDTO processarRespostaIA(String respostaIA) {
        try {
            // Limpar a resposta removendo possíveis caracteres extras
            String jsonLimpo = limparJSON(respostaIA);

            log.debug("JSON limpo para parsing: {}", jsonLimpo);

            // Converter JSON para DTO
            PlanejamentoIAResponseDTO resposta = gson.fromJson(jsonLimpo, PlanejamentoIAResponseDTO.class);

            // Validar resposta
            validarRespostaIA(resposta);

            return resposta;

        } catch (JsonSyntaxException e) {
            log.error("Erro ao fazer parse do JSON da IA: {}", e.getMessage());
            log.error("JSON recebido: {}", respostaIA);
            throw new RuntimeException("Resposta da IA não está em formato JSON válido", e);
        }
    }

    private String limparJSON(String json) {
        // Remove possíveis caracteres de markdown ou texto extra
        json = json.trim();

        // Remove ```json e ``` se existirem
        if (json.startsWith("```json")) {
            json = json.substring(7);
        }
        if (json.startsWith("```")) {
            json = json.substring(3);
        }
        if (json.endsWith("```")) {
            json = json.substring(0, json.length() - 3);
        }

        // Encontra o primeiro { e o último }
        int inicio = json.indexOf('{');
        int fim = json.lastIndexOf('}');

        if (inicio != -1 && fim != -1 && fim > inicio) {
            json = json.substring(inicio, fim + 1);
        }

        return json.trim();
    }

    private void validarRespostaIA(PlanejamentoIAResponseDTO resposta) {
        if (resposta == null) {
            throw new RuntimeException("Resposta da IA é nula");
        }

        if (resposta.getNomePlanejamento() == null || resposta.getNomePlanejamento().trim().isEmpty()) {
            throw new RuntimeException("Nome do planejamento não foi fornecido pela IA");
        }

        if (resposta.getCicloEstudo() == null || resposta.getCicloEstudo().getDisciplinas() == null ||
            resposta.getCicloEstudo().getDisciplinas().isEmpty()) {
            throw new RuntimeException("Disciplinas não foram fornecidas pela IA");
        }

        // Validar cada disciplina
        for (DisciplinaIADTO disciplina : resposta.getCicloEstudo().getDisciplinas()) {
            if (disciplina.getNome() == null || disciplina.getNome().trim().isEmpty()) {
                throw new RuntimeException("Nome da disciplina não foi fornecido pela IA");
            }
            if (disciplina.getPeso() == null || disciplina.getPeso() < 1 || disciplina.getPeso() > 5) {
                disciplina.setPeso(3); // Valor padrão
            }
            if (disciplina.getNivelConhecimento() == null || disciplina.getNivelConhecimento() < 1 || disciplina.getNivelConhecimento() > 5) {
                disciplina.setNivelConhecimento(3); // Valor padrão
            }
        }
    }

    private PlanejamentoDTO converterParaPlanejamentoDTO(PlanejamentoIAResponseDTO respostaIA, User user) {
        PlanejamentoDTO planejamentoDTO = new PlanejamentoDTO();

        planejamentoDTO.setNome(respostaIA.getNomePlanejamento());
        planejamentoDTO.setHorasDisponiveisPorSemana(respostaIA.getHorasDisponiveisPorSemana());
        planejamentoDTO.setMinutosDuracaoMaximaPorSessao(respostaIA.getMinutosDuracaoMaximaPorSessao());
        planejamentoDTO.setIntervalosRevisao(respostaIA.getIntervalosRevisao());

        // Converter ciclo de estudo
        if (respostaIA.getCicloEstudo() != null) {
            CicloEstudoDTO cicloEstudoDTO = new CicloEstudoDTO();
            cicloEstudoDTO.setNome(respostaIA.getCicloEstudo().getNome());

            // Converter disciplinas
            List<CicloEstudoDisciplinaDTO> disciplinasDTO = new ArrayList<>();

            for (DisciplinaIADTO disciplinaIA : respostaIA.getCicloEstudo().getDisciplinas()) {
                CicloEstudoDisciplinaDTO disciplinaDTO = new CicloEstudoDisciplinaDTO();

                // Criar ou buscar disciplina
                DisciplinaDTO disciplina = criarOuBuscarDisciplina(disciplinaIA.getNome(), user);
                disciplinaDTO.setDisciplina(disciplina);

                disciplinaDTO.setPeso(disciplinaIA.getPeso());
                disciplinaDTO.setNivelConhecimento(disciplinaIA.getNivelConhecimento());
                disciplinaDTO.setOrdem(disciplinaIA.getOrdem());

                // Converter tempo de estudo meta
                if (disciplinaIA.getTempoEstudoMeta() != null) {
                    try {
                        disciplinaDTO.setTempoEstudoMeta(LocalTime.parse(disciplinaIA.getTempoEstudoMeta()));
                    } catch (Exception e) {
                        log.warn("Erro ao converter tempo de estudo meta: {}", disciplinaIA.getTempoEstudoMeta());
                        disciplinaDTO.setTempoEstudoMeta(LocalTime.of(1, 0)); // 1 hora padrão
                    }
                }

                disciplinasDTO.add(disciplinaDTO);
            }

            cicloEstudoDTO.setDisciplinas(disciplinasDTO);
            planejamentoDTO.setCicloEstudo(cicloEstudoDTO);
        }

        return planejamentoDTO;
    }

    private DisciplinaDTO criarOuBuscarDisciplina(String nomeDisciplina, User user) {
        // Buscar disciplinas existentes do usuário
        List<DisciplinaDTO> disciplinasExistentes = disciplinaService.findAllByUserId(user.getId());

        // Verificar se já existe uma disciplina com nome similar
        for (DisciplinaDTO disciplina : disciplinasExistentes) {
            if (disciplina.getNome().equalsIgnoreCase(nomeDisciplina.trim())) {
                return disciplina;
            }
        }

        // Se não existe, criar nova disciplina
        DisciplinaDTO novaDisciplina = new DisciplinaDTO();
        novaDisciplina.setNome(nomeDisciplina.trim());

        return disciplinaService.save(novaDisciplina, user);
    }

}