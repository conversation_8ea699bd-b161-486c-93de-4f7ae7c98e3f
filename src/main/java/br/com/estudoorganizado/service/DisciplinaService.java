package br.com.estudoorganizado.service;

import br.com.estudoorganizado.dto.DisciplinaDTO;
import br.com.estudoorganizado.dto.DisciplinaTopicoDTO;
import br.com.estudoorganizado.exception.ResourceNotFoundException;
import br.com.estudoorganizado.model.Disciplina;
import br.com.estudoorganizado.model.User;
import br.com.estudoorganizado.repository.DisciplinaRepository;
import br.com.estudoorganizado.repository.DisciplinaTopicoRepository;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DisciplinaService {

    private final DisciplinaRepository disciplinaRepository;
    private final DisciplinaTopicoRepository disciplinaTopicoRepository;
    private final ModelMapper modelMapper;

    public DisciplinaDTO save(DisciplinaDTO disciplinaDTO, User user) {
        validarDados(disciplinaDTO);
        Disciplina disciplinaEntity = modelMapper.map(disciplinaDTO, Disciplina.class);
        disciplinaEntity.setUser(user);
        Disciplina savedDisciplina = disciplinaRepository.save(disciplinaEntity);
        return modelMapper.map(savedDisciplina, DisciplinaDTO.class);
    }

    public List<DisciplinaDTO> findAllByUserId(Long userId) {
        return disciplinaRepository.findByUserId(userId).stream()
                .map(disciplina -> modelMapper.map(disciplina, DisciplinaDTO.class))
                .collect(Collectors.toList());
    }

    public DisciplinaDTO findByIdAndUserId(Long id, Long userId) {
        Disciplina disciplina = disciplinaRepository.findByIdAndUserId(id, userId)
                .orElseThrow(() -> new ResourceNotFoundException("Disciplina não encontrada com o id: " + id));
        return modelMapper.map(disciplina, DisciplinaDTO.class);
    }

    public DisciplinaDTO update(Long id, Long userId, DisciplinaDTO disciplinaDTO) {
        validarDados(disciplinaDTO);
        Disciplina existingDisciplina = disciplinaRepository.findByIdAndUserId(id, userId)
                .orElseThrow(() -> new ResourceNotFoundException("Disciplina não encontrada com o id: " + id));

        existingDisciplina.setNome(disciplinaDTO.getNome());
        Disciplina updatedDisciplina = disciplinaRepository.save(existingDisciplina);
        return modelMapper.map(updatedDisciplina, DisciplinaDTO.class);
    }

    public void delete(Long id, Long userId) {
        if (!disciplinaRepository.existsByIdAndUserId(id, userId)) {
            throw new ResourceNotFoundException("Disciplina não encontrada com o id: " + id);
        }
        disciplinaRepository.deleteById(id);
    }

    public List<DisciplinaTopicoDTO> consultarTopicosPorDisciplinaIdAndUserId(Long disciplinaId, Long userId) {
        return disciplinaTopicoRepository.findByDisciplinaIdAndUserId(disciplinaId, userId).stream()
                .map(disciplinaTopico -> modelMapper.map(disciplinaTopico, DisciplinaTopicoDTO.class))
                .collect(Collectors.toList());
    }

    private void validarDados(DisciplinaDTO disciplinaDTO) {
        if (disciplinaDTO == null) {
            throw new IllegalArgumentException("Disciplina não informada!");
        }
        if (Objects.isNull(disciplinaDTO.getNome()) || disciplinaDTO.getNome().isEmpty()) {
            throw new IllegalArgumentException("Nome da disciplina não foi informada!");
        }
    }
}
